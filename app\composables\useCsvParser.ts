import Papa from 'papaparse'
import { z } from 'zod'
import type { 
  StudentRecord, 
  ItemResponse, 
  QuestionMetadata, 
  ZipGradeCsvData, 
  CsvParseResult 
} from '~/types/csv'

// Validation schemas
const MobileFormatSchema = z.object({
  ZipGradeID: z.string(),
  ExternalID: z.string().optional(),
  NumberCorrect: z.coerce.number(),
  NumbeOfQuestions: z.coerce.number(), // Note: handles typo in ZipGrade mobile export
})

const WebFormatSchema = z.object({
  StudentID: z.string(),
  CustomID: z.string().optional(),
  'Earned Points': z.coerce.number(),
  'Possible Points': z.coerce.number(),
})


export const useCsvParser = () => {
  const parseFile = async (file: File): Promise<CsvParseResult> => {
    const result: CsvParseResult = {
      success: false,
      data: null,
      format: null,
      errors: [],
      warnings: []
    }

    try {
      // Parse CSV file
      const csvText = await readFileAsText(file)
      const parsed = Papa.parse<Record<string, string>>(csvText, {
        header: true,
        skipEmptyLines: true,
        dynamicTyping: false // Keep as strings for better control
      })

      if (parsed.errors && parsed.errors.length > 0) {
        result.errors.push(...parsed.errors.map((err: any) => `CSV Parse Error: ${err.message}`))
        return result
      }

      if (!parsed.data || parsed.data.length === 0) {
        result.errors.push('CSV file is empty or contains no valid data')
        return result
      }

      // Detect format and validate
      const formatDetection = detectFormat(parsed.data)
      if (!formatDetection.success) {
        result.errors.push(...formatDetection.errors)
        return result
      }

      result.format = formatDetection.format!
      result.warnings.push(...formatDetection.warnings)

      // Process data based on format
      const processedData = formatDetection.format === 'mobile' 
        ? processMobileFormat(parsed.data)
        : processWebFormat(parsed.data)

      if (!processedData.success) {
        result.errors.push(...processedData.errors)
        return result
      }

      result.data = processedData.data!
      result.success = true
      return result

    } catch (error) {
      result.errors.push(`File processing error: ${error instanceof Error ? error.message : 'Unknown error'}`)
      return result
    }
  }

  const detectFormat = (data: Record<string, string>[]): {
    success: boolean
    format: 'mobile' | 'web' | null
    errors: string[]
    warnings: string[]
  } => {
    const result = {
      success: false,
      format: null as 'mobile' | 'web' | null,
      errors: [] as string[],
      warnings: [] as string[]
    }

    if (data.length === 0) {
      result.errors.push('No data rows found in CSV')
      return result
    }

    const firstRow = data[0]
    if (!firstRow) {
      result.errors.push('No data rows found in CSV')
      return result
    }
    const headers = Object.keys(firstRow)

    // Check for mobile format indicators
    const hasMobileHeaders = headers.includes('ZipGradeID') && 
                            headers.includes('NumberCorrect') &&
                            (headers.includes('NumbeOfQuestions') || headers.includes('NumberOfQuestions'))

    // Handle the typo in mobile format
    if (headers.includes('NumbeOfQuestions')) {
      result.warnings.push('Detected typo in header "NumbeOfQuestions" - this is a known ZipGrade mobile app export issue')
    }

    // Check for web format indicators
    const hasWebHeaders = headers.includes('StudentID') && 
                         headers.includes('Earned Points') && 
                         headers.includes('Possible Points')

    if (hasMobileHeaders && hasWebHeaders) {
      result.errors.push('Ambiguous format: CSV contains headers from both mobile and web formats')
      return result
    }

    if (hasMobileHeaders) {
      result.format = 'mobile'
      result.success = true
    } else if (hasWebHeaders) {
      result.format = 'web'
      result.success = true
    } else {
      result.errors.push('Unrecognized CSV format. Expected ZipGrade mobile app or web app export format.')
      return result
    }

    return result
  }

  const processMobileFormat = (data: Record<string, string>[]): {
    success: boolean
    data: ZipGradeCsvData | null
    errors: string[]
  } => {
    const result = {
      success: false,
      data: null as ZipGradeCsvData | null,
      errors: [] as string[]
    }

    try {
      const students: StudentRecord[] = []
      const questionsMap = new Map<number, QuestionMetadata>()

      for (const [index, row] of data.entries()) {
        // Validate basic structure
        const validation = MobileFormatSchema.safeParse(row)
        if (!validation.success) {
          result.errors.push(`Row ${index + 1}: Invalid mobile format - ${validation.error.issues.map(i => i.message).join(', ')}`)
          continue
        }

        const validRow = validation.data
        
        // Extract question data and student responses
        const responses: ItemResponse[] = []
        let questionNumber = 1

        // Find all question columns (Key1, Key2, etc.)
        while (row[`Key${questionNumber}`] !== undefined) {
          const correctAnswer = row[`Key${questionNumber}`] || ''
          const studentResponse = row[`Stu${questionNumber}`] || ''
          const earnedPoints = Number(row[`EarnedPt${questionNumber}`] || 0)
          const possiblePoints = Number(row[`PossPt${questionNumber}`] || 1)

          // Store question metadata
          if (!questionsMap.has(questionNumber)) {
            questionsMap.set(questionNumber, {
              questionNumber,
              correctAnswer,
              maxPoints: possiblePoints
            })
          }

          // Create item response
          responses.push({
            questionNumber,
            studentResponse,
            correctAnswer,
            isCorrect: studentResponse.toLowerCase() === correctAnswer.toLowerCase(),
            pointsEarned: earnedPoints,
            pointsPossible: possiblePoints
          })

          questionNumber++
        }

        // Handle typo in column name
        const totalQuestions = validRow.NumbeOfQuestions || Number(row.NumberOfQuestions || questionNumber - 1)
        const totalScore = validRow.NumberCorrect
        const percentage = totalQuestions > 0 ? (totalScore / totalQuestions) * 100 : 0

        students.push({
          id: validRow.ZipGradeID,
          externalId: validRow.ExternalID,
          totalScore,
          totalPossible: totalQuestions,
          percentage,
          responses
        })
      }

      if (students.length === 0) {
        result.errors.push('No valid student records found')
        return result
      }

      // Calculate metadata
      const scores = students.map(s => s.percentage)
      const averageScore = scores.reduce((sum, score) => sum + score, 0) / scores.length
      const variance = scores.reduce((sum, score) => sum + Math.pow(score - averageScore, 2), 0) / scores.length
      const standardDeviation = Math.sqrt(variance)

      result.data = {
        students,
        questions: Array.from(questionsMap.values()).sort((a, b) => a.questionNumber - b.questionNumber),
        totalStudents: students.length,
        totalQuestions: questionsMap.size,
        format: 'mobile',
        metadata: {
          averageScore,
          standardDeviation
        }
      }

      result.success = true
      return result

    } catch (error) {
      result.errors.push(`Mobile format processing error: ${error instanceof Error ? error.message : 'Unknown error'}`)
      return result
    }
  }

  const processWebFormat = (data: Record<string, string>[]): {
    success: boolean
    data: ZipGradeCsvData | null
    errors: string[]
  } => {
    const result = {
      success: false,
      data: null as ZipGradeCsvData | null,
      errors: [] as string[]
    }

    try {
      const students: StudentRecord[] = []
      const questionsMap = new Map<number, QuestionMetadata>()

      for (const [index, row] of data.entries()) {
        // Validate basic structure
        const validation = WebFormatSchema.safeParse(row)
        if (!validation.success) {
          result.errors.push(`Row ${index + 1}: Invalid web format - ${validation.error.issues.map(i => i.message).join(', ')}`)
          continue
        }

        const validRow = validation.data
        
        // Extract question data and student responses
        const responses: ItemResponse[] = []
        let questionNumber = 1

        // Find all question columns (PriKey1, PriKey2, etc.)
        while (row[`PriKey${questionNumber}`] !== undefined) {
          const correctAnswer = row[`PriKey${questionNumber}`] || ''
          const studentResponse = row[`Stu${questionNumber}`] || ''
          const points = Number(row[`Points${questionNumber}`] || 1)
          const mark = row[`Mark${questionNumber}`] || ''
          const isCorrect = mark.toUpperCase() === 'C'
          const earnedPoints = isCorrect ? points : 0

          // Store question metadata
          if (!questionsMap.has(questionNumber)) {
            questionsMap.set(questionNumber, {
              questionNumber,
              correctAnswer,
              maxPoints: points
            })
          }

          // Create item response
          responses.push({
            questionNumber,
            studentResponse,
            correctAnswer,
            isCorrect,
            pointsEarned: earnedPoints,
            pointsPossible: points
          })

          questionNumber++
        }

        const totalScore = validRow['Earned Points']
        const totalPossible = validRow['Possible Points']
        const percentage = totalPossible > 0 ? (totalScore / totalPossible) * 100 : 0

        students.push({
          id: validRow.StudentID,
          externalId: validRow.CustomID,
          totalScore,
          totalPossible,
          percentage,
          responses
        })
      }

      if (students.length === 0) {
        result.errors.push('No valid student records found')
        return result
      }

      // Calculate metadata
      const scores = students.map(s => s.percentage)
      const averageScore = scores.reduce((sum, score) => sum + score, 0) / scores.length
      const variance = scores.reduce((sum, score) => sum + Math.pow(score - averageScore, 2), 0) / scores.length
      const standardDeviation = Math.sqrt(variance)

      result.data = {
        students,
        questions: Array.from(questionsMap.values()).sort((a, b) => a.questionNumber - b.questionNumber),
        totalStudents: students.length,
        totalQuestions: questionsMap.size,
        format: 'web',
        metadata: {
          averageScore,
          standardDeviation
        }
      }

      result.success = true
      return result

    } catch (error) {
      result.errors.push(`Web format processing error: ${error instanceof Error ? error.message : 'Unknown error'}`)
      return result
    }
  }

  const readFileAsText = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = (e) => resolve(e.target?.result as string)
      reader.onerror = () => reject(new Error('Failed to read file'))
      reader.readAsText(file)
    })
  }

  return {
    parseFile
  }
}
