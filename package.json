{"name": "itemify", "private": true, "type": "module", "packageManager": "pnpm@10.15.0", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "typecheck": "nuxt typecheck", "format": "biome format --write .", "lint": "biome lint --write ."}, "dependencies": {"@nuxt/image": "^1.11.0", "@nuxt/ui": "^3.3.2", "@pinia/nuxt": "^0.11.2", "@vueuse/motion": "^3.0.3", "@vueuse/nuxt": "^13.7.0", "date-fns": "^4.1.0", "nuxt": "^4.0.3", "nuxt-security": "^2.4.0", "papaparse": "^5.5.3", "vue": "^3.5.19", "xlsx": "^0.18.5", "zod": "^4.0.17"}, "devDependencies": {"@biomejs/biome": "^2.2.0", "@types/papaparse": "^5.3.16", "@vite-pwa/nuxt": "^1.0.4", "typescript": "^5.9.2", "vue-tsc": "^3.0.6"}, "resolutions": {"unimport": "5.2.0"}}