// CSV parsing types
export interface StudentRecord {
  id: string
  name?: string
  externalId?: string
  totalScore: number
  totalPossible: number
  percentage: number
  responses: ItemResponse[]
}

export interface ItemResponse {
  questionNumber: number
  studentResponse: string
  correctAnswer: string
  isCorrect: boolean
  pointsEarned: number
  pointsPossible: number
}

export interface QuestionMetadata {
  questionNumber: number
  correctAnswer: string
  maxPoints: number
}

export interface ZipGradeCsvData {
  students: StudentRecord[]
  questions: QuestionMetadata[]
  totalStudents: number
  totalQuestions: number
  format: 'mobile' | 'web'
  metadata: {
    averageScore: number
    standardDeviation: number
  }
}

export interface CsvParseResult {
  success: boolean
  data: ZipGradeCsvData | null
  format: 'mobile' | 'web' | null
  errors: string[]
  warnings: string[]
}
