<template>
  <div class="min-h-screen bg-background flex flex-col">
    <main class="flex-1 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div class="max-w-4xl w-full">
        <div class="text-center mb-12">
          <h2 
            v-motion
            :initial="{ y: 50, opacity: 0 }"
            :enter="{ y: 0, opacity: 1, transition: { delay: 300, duration: 800 } }"
            class="text-4xl sm:text-5xl lg:text-6xl font-bold text-foreground mb-6"
          >
            Analyze Your
            <span 
              v-motion
              :initial="{ scale: 0.8, opacity: 0 }"
              :enter="{ scale: 1, opacity: 1, transition: { delay: 600, duration: 800 } }"
              class="text-primary"
            >
              Quarterly Tests
            </span>
          </h2>
          <p 
            v-motion
            :initial="{ y: 30, opacity: 0 }"
            :enter="{ y: 0, opacity: 1, transition: { delay: 800, duration: 800 } }"
            class="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed"
          >
            Upload your ZipGrade CSV export and get comprehensive item analysis reports designed for DepEd public high school teachers
          </p>
        </div>

        <div 
          v-motion
          :initial="{ y: 80, opacity: 0, scale: 0.95 }"
          :enter="{ y: 0, opacity: 1, scale: 1, transition: { delay: 1000, duration: 800 } }"
        >
          <ItemAnalysisUploadArea 
            @file-processed="handleFileProcessed"
          />
        </div>

      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
useSeoMeta({
  title: 'Itemify',
  ogTitle: 'Itemify',
  description: 'Upload your ZipGrade CSV export and get comprehensive item analysis reports designed for DepEd public high school teachers.',
  ogDescription: 'Upload your ZipGrade CSV export and get comprehensive item analysis reports designed for DepEd public high school teachers.',
  ogImage: '/og-image.jpg',
  twitterCard: 'summary_large_image',
})

definePageMeta({
  title: 'Itemify',
  description: 'Professional item analysis tool for DepEd public high school teachers. Upload ZipGrade CSV files and get comprehensive test statistics and teaching insights.',
})

import type { ZipGradeCsvData } from '~/types/csv'

const uploadedData = ref<ZipGradeCsvData | null>(null)
const showAnalysis = ref(false)

const handleFileProcessed = (data: ZipGradeCsvData) => {
  uploadedData.value = data
  showAnalysis.value = true
  
  // Store raw CSV data in session storage for the analysis page to process
  if (import.meta.client) {
    sessionStorage.setItem('itemAnalysisRawData', JSON.stringify(data))
    sessionStorage.removeItem('itemAnalysisData')
  }

  navigateTo('/analysis')
}
</script>
