<template>
  <div class="min-h-screen bg-background">
    <main class="container mx-auto px-4 py-8">
      <!-- Loading State -->
      <div v-if="pending" class="flex items-center justify-center min-h-[400px]">
        <div class="text-center">
          <UIcon name="i-line-md-loading-twotone-loop" class="w-12 h-12 text-primary mx-auto mb-4" />
          <h2 class="text-xl font-semibold text-card-foreground mb-2">Processing Analysis</h2>
          <p class="text-muted-foreground">Analyzing your test data...</p>
        </div>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="flex items-center justify-center min-h-[400px]">
        <UAlert
          color="error"
          title="Analysis Error"
          class="max-w-md"
        >
          <template #description>
            {{ error.message || 'Failed to load analysis results. Please try uploading your file again.' }}
          </template>
          <template #actions>
            <UButton
              color="error"
              variant="soft"
              size="sm"
              @click="navigateTo('/')"
            >
              Upload New File
            </UButton>
          </template>
        </UAlert>
      </div>

      <!-- Analysis Results -->
      <div v-else-if="analysisData" class="space-y-8">
        <!-- Back to Upload -->
        <div class="flex items-center gap-4">
          <UButton
            variant="ghost"
            size="sm"
            @click="navigateTo('/')"
          >
            <UIcon name="i-line-md-arrow-left" class="w-4 h-4 mr-2" />
            Upload New File
          </UButton>
        </div>

        <!-- Summary Table -->
        <ItemAnalysisSummaryTable
          :analysis="analysisData"
          :competency-mapping="competencyMapping"
          @export="handleExport"
          @update:competency-mapping="updateCompetencyMapping"
        />

        <!-- Competency Analysis (if competencies are assigned) -->
        <ItemAnalysisCompetencyAnalysisCard
          v-if="analysisData.competencyAnalysis"
          :competency-analysis="analysisData.competencyAnalysis"
        />
      </div>

      <!-- No Data State -->
      <div v-else class="flex items-center justify-center min-h-[400px]">
        <div class="text-center max-w-md">
          <UIcon name="i-line-md-document-list" class="w-16 h-16 text-muted-foreground mx-auto mb-4" />
          <h2 class="text-xl font-semibold text-card-foreground mb-2">No Analysis Data</h2>
          <p class="text-muted-foreground mb-4">
            No analysis results found. Please upload a ZipGrade CSV file to get started.
          </p>
          <UButton @click="navigateTo('/')">
            Upload CSV File
          </UButton>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { TestAnalysis } from '~/types/statistics'
import type { ZipGradeCsvData } from '~/types/csv'

// Meta and SEO
definePageMeta({
  title: 'Item Analysis Results'
})

useSeoMeta({
  title: 'Item Analysis Results - Itemify',
  description: 'Comprehensive statistical analysis of test items for DepEd public high school teachers'
})

// Composables
const { analyzeTest } = useStatistics()

// Reactive state
const pending = ref(false)
const error = ref<Error | null>(null)
const analysisData = ref<TestAnalysis | null>(null)
const competencyMapping = ref<Map<number, string>>(new Map())

// Methods
const updateCompetencyMapping = (newMapping: Map<number, string>) => {
  competencyMapping.value = newMapping
  
  // Regenerate analysis with competency data
  const storedData = sessionStorage.getItem('itemAnalysisRawData')
  if (storedData) {
    try {
      const rawData: ZipGradeCsvData = JSON.parse(storedData)
      analysisData.value = analyzeTest(rawData, newMapping)
      
      // Update stored analysis data
      sessionStorage.setItem('itemAnalysisData', JSON.stringify(analysisData.value))
    } catch (err) {
      console.error('Failed to regenerate analysis with competency data:', err)
    }
  }
}

const handleExport = async (format: 'excel' | 'csv') => {
  if (!analysisData.value) return
  
  try {
    // TODO: Implement actual export functionality
    console.log(`Exporting as ${format}...`)
    
    // This would use the xlsx library to generate Excel files
    // or convert the data to CSV format for download
    
  } catch (err) {
    console.error('Export failed:', err)
    // TODO: Show error toast
  }
}

// Load analysis data from session storage
onMounted(() => {
  try {
    // First try to get already processed analysis data
    const storedAnalysis = sessionStorage.getItem('itemAnalysisData')
    if (storedAnalysis) {
      analysisData.value = JSON.parse(storedAnalysis)
      return
    }
    
    // If no processed data, look for raw CSV data and process it
    const storedRawData = sessionStorage.getItem('itemAnalysisRawData')
    if (storedRawData) {
      const rawData: ZipGradeCsvData = JSON.parse(storedRawData)
      pending.value = true
      
      // Process the raw data into analysis
      analysisData.value = analyzeTest(rawData)
      
      // Store the processed analysis for future use
      sessionStorage.setItem('itemAnalysisData', JSON.stringify(analysisData.value))
      pending.value = false
    } else {
      // No data available, redirect to upload page
      navigateTo('/')
    }
  } catch (err) {
    error.value = err as Error
    pending.value = false
  }
})
</script>
