<template>
  <div class="space-y-6">
    <div 
      ref="dropZone"
      class="border-2 border-dashed rounded-lg p-8 text-center transition-all duration-300"
      :class="[
        isDragOver 
          ? 'border-primary bg-primary/5 scale-105' 
          : 'border-border hover:border-primary/50 hover:bg-muted/30',
        isProcessing ? 'pointer-events-none opacity-75' : 'cursor-pointer'
      ]"
      @click="triggerFileInput"
      @dragover.prevent="handleDragOver"
      @dragleave.prevent="handleDragLeave"
      @drop.prevent="handleDrop"
    >
      <input
        ref="fileInput"
        type="file"
        accept=".csv"
        class="hidden"
        @change="handleFileSelect"
      />

      <!-- Upload Icon and Status -->
      <div class="mb-6">
        <UIcon 
          v-if="!isProcessing"
          :name="isDragOver ? 'i-line-md-cloud-down' : 'i-line-md-cloud-upload-outline-loop'" 
          class="w-16 h-16 mx-auto transition-all duration-300"
          :class="isDragOver ? 'text-primary' : 'text-muted-foreground'"
        />
        <UIcon 
          v-else
          name="i-line-md-loading-loop" 
          class="w-16 h-16 text-primary mx-auto"
        />
      </div>

      <!-- Upload Text -->
      <div class="space-y-3">
        <h3 class="text-xl font-semibold text-card-foreground">
          {{ isProcessing ? 'Processing Your File...' : 'Upload Your ZipGrade File' }}
        </h3>
        <p class="text-muted-foreground">
          {{ 
            isProcessing 
              ? 'Parsing CSV data and validating format' 
              : isDragOver 
                ? 'Drop your CSV file here' 
                : 'Drag and drop your CSV file here, or click to browse'
          }}
        </p>
        
        <UButton 
          v-if="!isProcessing"
          size="xl" 
          icon="i-line-md-document-add"
          :disabled="isProcessing"
          class="mt-4"
        >
          Choose File
        </UButton>
      </div>

      <!-- File Format Info -->
      <div class="mt-6 text-sm text-muted-foreground/70">
        <p>Supports CSV files exported from:</p>
        <div class="flex justify-center gap-4 mt-2">
          <span class="px-3 py-1 bg-muted rounded-full text-xs">ZipGrade Mobile App</span>
          <span class="px-3 py-1 bg-muted rounded-full text-xs">ZipGrade Web App</span>
        </div>
      </div>
    </div>

    <!-- Processing Status -->
    <div v-if="isProcessing" class="bg-card border rounded-lg p-4">
      <div class="flex items-center gap-3">
        <UIcon name="i-line-md-loading-loop" class="w-5 h-5 text-primary animate-spin" />
        <div class="flex-1">
          <p class="font-medium text-card-foreground">Processing {{ selectedFile?.name }}</p>
          <p class="text-sm text-muted-foreground">
            {{ processingStep }}
          </p>
        </div>
      </div>
      
      <!-- Progress Bar -->
      <div class="mt-3 bg-muted rounded-full h-2">
        <div 
          class="bg-primary h-2 rounded-full transition-all duration-500"
          :style="{ width: `${processingProgress}%` }"
        />
      </div>
    </div>

    <!-- Error Display -->
    <UAlert 
      v-if="errors.length > 0"
      color="error"
      title="Upload Error"
    >
      <template #description>
        <div class="space-y-2">
          <p>There was a problem processing your file:</p>
          <ul class="list-disc list-inside space-y-1">
            <li v-for="error in errors" :key="error" class="text-sm">
              {{ error }}
            </li>
          </ul>
        </div>
      </template>
    </UAlert>

    <!-- Warnings Display -->
    <UAlert 
      v-if="warnings.length > 0"
      color="warning"
      title="Format Warnings"
    >
      <template #description>
        <div class="space-y-2">
          <p>Your file was processed successfully, but we noticed:</p>
          <ul class="list-disc list-inside space-y-1">
            <li v-for="warning in warnings" :key="warning" class="text-sm">
              {{ warning }}
            </li>
          </ul>
        </div>
      </template>
    </UAlert>

    <!-- Success Display -->
    <UAlert 
      v-if="uploadSuccess && parsedData"
      color="success"
      title="File Processed Successfully"
    >
      <template #description>
        <div class="space-y-2">
          <p>Your ZipGrade file has been processed:</p>
          <div class="grid grid-cols-2 gap-4 mt-3 text-sm">
            <div>
              <span class="font-medium">Format:</span> 
              {{ parsedData.format === 'mobile' ? 'ZipGrade Mobile App' : 'ZipGrade Web App' }}
            </div>
            <div>
              <span class="font-medium">Students:</span> 
              {{ parsedData.totalStudents }}
            </div>
            <div>
              <span class="font-medium">Questions:</span> 
              {{ parsedData.totalQuestions }}
            </div>
            <div>
              <span class="font-medium">Average Score:</span> 
              {{ parsedData.metadata.averageScore.toFixed(1) }}%
            </div>
          </div>
        </div>
      </template>
    </UAlert>
  </div>
</template>

<script setup lang="ts">
import { useCsvParser } from '~/composables/useCsvParser'
import type { ZipGradeCsvData, CsvParseResult } from '~/types/csv'

// Component props
interface Props {
  maxFileSize?: number // in MB
}

const props = withDefaults(defineProps<Props>(), {
  maxFileSize: 10
})

// Component emits
interface Emits {
  (e: 'fileProcessed', data: ZipGradeCsvData): void
  (e: 'error', errors: string[]): void
}

const emit = defineEmits<Emits>()

// Reactive state
const isDragOver = ref(false)
const isProcessing = ref(false)
const processingStep = ref('')
const processingProgress = ref(0)
const selectedFile = ref<File | null>(null)
const uploadSuccess = ref(false)
const errors = ref<string[]>([])
const warnings = ref<string[]>([])
const parsedData = ref<ZipGradeCsvData | null>(null)

// Refs
const dropZone = ref<HTMLElement>()
const fileInput = ref<HTMLInputElement>()

// Composables
const { parseFile } = useCsvParser()

// Methods
const triggerFileInput = () => {
  if (!isProcessing.value) {
    fileInput.value?.click()
  }
}

const handleDragOver = (event: DragEvent) => {
  if (isProcessing.value) return
  isDragOver.value = true
  if (event.dataTransfer) {
    event.dataTransfer.dropEffect = 'copy'
  }
}

const handleDragLeave = (event: DragEvent) => {
  // Only set to false if we're leaving the drop zone completely
  if (!dropZone.value?.contains(event.relatedTarget as Node)) {
    isDragOver.value = false
  }
}

const handleDrop = (event: DragEvent) => {
  isDragOver.value = false
  if (isProcessing.value) return
  
  const files = event.dataTransfer?.files
  if (files && files.length > 0) {
    processFile(files[0])
  }
}

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  const files = target.files
  if (files && files.length > 0) {
    processFile(files[0])
  }
}

const processFile = async (file: File) => {
  // Reset state
  uploadSuccess.value = false
  errors.value = []
  warnings.value = []
  parsedData.value = null
  selectedFile.value = file
  
  // Validate file
  const validationErrors = validateFile(file)
  if (validationErrors.length > 0) {
    errors.value = validationErrors
    emit('error', validationErrors)
    return
  }
  
  // Start processing
  isProcessing.value = true
  processingProgress.value = 0
  
  try {
    // Step 1: Reading file
    processingStep.value = 'Reading file...'
    processingProgress.value = 25
    await sleep(500) // Small delay for UX
    
    // Step 2: Parsing CSV
    processingStep.value = 'Parsing CSV data...'
    processingProgress.value = 50
    
    const result: CsvParseResult = await parseFile(file)
    
    // Step 3: Validating format
    processingStep.value = 'Validating format...'
    processingProgress.value = 75
    await sleep(300)
    
    // Step 4: Complete
    processingStep.value = 'Processing complete!'
    processingProgress.value = 100
    await sleep(300)
    
    if (result.success && result.data) {
      // Success
      uploadSuccess.value = true
      warnings.value = result.warnings
      parsedData.value = result.data
      emit('fileProcessed', result.data)
    } else {
      // Error
      errors.value = result.errors
      emit('error', result.errors)
    }
    
  } catch (error) {
    errors.value = [`Unexpected error: ${error instanceof Error ? error.message : 'Unknown error'}`]
    emit('error', errors.value)
  } finally {
    isProcessing.value = false
    processingStep.value = ''
    processingProgress.value = 0
  }
}

const validateFile = (file: File): string[] => {
  const errors: string[] = []
  
  // Check file type
  if (!file.name.toLowerCase().endsWith('.csv')) {
    errors.push('Please select a CSV file')
  }
  
  // Check file size
  const maxSizeBytes = props.maxFileSize * 1024 * 1024
  if (file.size > maxSizeBytes) {
    errors.push(`File size must be less than ${props.maxFileSize}MB`)
  }
  
  // Check if file is empty
  if (file.size === 0) {
    errors.push('File appears to be empty')
  }
  
  return errors
}

const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

// Cleanup on unmount
onUnmounted(() => {
  if (fileInput.value) {
    fileInput.value.value = ''
  }
})
</script>
