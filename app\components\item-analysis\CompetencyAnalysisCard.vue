<template>
  <UCard>
    <template #header>
      <div class="flex items-center gap-2">
        <UIcon name="i-line-md-list" class="w-5 h-5 text-primary" />
        <h3 class="font-semibold">Learning Competency Analysis</h3>
      </div>
    </template>

    <div class="space-y-4">
      <p class="text-muted-foreground text-sm">
        Analysis of student mastery by learning competency
      </p>

      <div class="space-y-3">
        <div
          v-for="competency in competencyAnalysis"
          :key="competency.rank"
          class="p-4 border rounded-lg"
        >
          <div class="flex items-start justify-between mb-2">
            <div class="flex-1">
              <h4 class="font-medium text-card-foreground">
                {{ competency.learningCompetency }}
              </h4>
              <p class="text-sm text-muted-foreground">
                Items: {{ competency.items.join(', ') }}
              </p>
            </div>
            <UBadge :color="getMasteryColor(competency.averageMasteryLevel)" variant="soft">
              {{ competency.averageMasteryLevel.toFixed(1) }}% Mastery
            </UBadge>
          </div>

          <div class="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span class="text-muted-foreground">Difficulty:</span>
              <span class="ml-2 font-medium">
                {{ (competency.averageDifficulty * 100).toFixed(1) }}%
              </span>
              <UBadge
                :color="getDifficultyColor(competency.averageDifficulty)"
                variant="soft"
                size="xs"
                class="ml-2"
              >
                {{ competency.averageDifficultyLevel }}
              </UBadge>
            </div>
            <div>
              <span class="text-muted-foreground">Discrimination:</span>
              <span class="ml-2 font-medium">
                {{ competency.averageDiscrimination.toFixed(3) }}
              </span>
              <UBadge
                :color="getDiscriminationColor(competency.averageDiscrimination)"
                variant="soft"
                size="xs"
                class="ml-2"
              >
                {{ competency.averageDiscriminationLevel }}
              </UBadge>
            </div>
          </div>
        </div>
      </div>
    </div>
  </UCard>
</template>

<script setup lang="ts">
import type { CompetencyAnalysis } from '~/types/statistics'

// Component props
interface Props {
  competencyAnalysis: CompetencyAnalysis[]
}

const props = defineProps<Props>()

// Helper functions
const getMasteryColor = (mastery: number): string => {
  if (mastery >= 75) return 'green'
  if (mastery >= 50) return 'yellow'
  return 'red'
}

const getDifficultyColor = (difficulty: number): string => {
  if (difficulty > 0.80) return 'green'
  if (difficulty >= 0.75) return 'blue'
  if (difficulty >= 0.50) return 'yellow'
  if (difficulty >= 0.25) return 'orange'
  return 'red'
}

const getDiscriminationColor = (discrimination: number): string => {
  if (discrimination >= 0.40) return 'green'
  if (discrimination >= 0.30) return 'blue'
  if (discrimination >= 0.20) return 'yellow'
  if (discrimination >= 0.10) return 'orange'
  return 'red'
}
</script>
