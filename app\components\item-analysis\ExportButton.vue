<template>
  <div class="flex items-center gap-2">
    <UButton
      variant="outline"
      size="sm"
      @click="handleExport('csv')"
      :loading="isExporting"
    >
      <UIcon name="i-line-md-download" class="w-4 h-4 mr-2" />
      Export CSV
    </UButton>
    <UButton
      variant="outline"
      size="sm"
      @click="handleExport('excel')"
      :loading="isExporting"
    >
      <UIcon name="i-line-md-download" class="w-4 h-4 mr-2" />
      Export Excel
    </UButton>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { TestAnalysis, SummaryReportRow } from '~/types/statistics'

// Component props
interface Props {
  data: SummaryReportRow[]
  analysis: TestAnalysis
  competencyMapping?: Map<number, string>
}

const props = defineProps<Props>()

// Component emits
interface Emits {
  (e: 'export', format: 'excel' | 'csv'): void
}

const emit = defineEmits<Emits>()

// Reactive state
const isExporting = ref(false)

// Methods
const handleExport = async (format: 'excel' | 'csv') => {
  try {
    isExporting.value = true
    emit('export', format)
  } finally {
    // Reset loading state after a short delay
    setTimeout(() => {
      isExporting.value = false
    }, 1000)
  }
}
</script>
