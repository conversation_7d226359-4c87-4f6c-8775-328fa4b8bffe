import type { ZipGradeCsvData, StudentRecord, ItemResponse } from '~/types/csv'
import type { 
  ItemAnalysis, 
  TestAnalysis, 
  TestStatistics, 
  CompetencyAnalysis, 
  SummaryReportRow 
} from '~/types/statistics'

// Re-export types for use in components
export type { 
  ItemAnalysis, 
  TestAnalysis, 
  TestStatistics, 
  CompetencyAnalysis, 
  SummaryReportRow 
}

export const useStatistics = () => {
  const analyzeTest = (
    data: ZipGradeCsvData,
    competencyMapping?: Map<number, string>,
  ): TestAnalysis => {
    const testStats = calculateTestStatistics(data)
    const itemAnalyses = data.questions.map((question) =>
      calculateItemAnalysis(question.questionNumber, data),
    )

    const competencyAnalysis = competencyMapping
      ? calculateCompetencyAnalysis(data, competencyMapping)
      : undefined

    const recommendations = generateRecommendations(testStats, itemAnalyses)

    return {
      testStatistics: testStats,
      itemAnalyses,
      competencyAnalysis,
      recommendations,
    }
  }

  const calculateTestStatistics = (data: ZipGradeCsvData): TestStatistics => {
    const scores = data.students.map((student) => student.percentage)
    const sortedScores = [...scores].sort((a, b) => a - b)

    // Basic statistics
    const mean = scores.reduce((sum, score) => sum + score, 0) / scores.length
    const variance =
      scores.reduce((sum, score) => sum + Math.pow(score - mean, 2), 0) / scores.length
    const standardDeviation = Math.sqrt(variance)

    // Median
    const median =
      sortedScores.length % 2 === 0
        ? ((sortedScores[sortedScores.length / 2 - 1] ?? 0) + (sortedScores[sortedScores.length / 2] ?? 0)) / 2
        : sortedScores[Math.floor(sortedScores.length / 2)] ?? 0

    // Mode (most frequent score, rounded to nearest integer)
    const roundedScores = scores.map((score) => Math.round(score))
    const frequency = new Map<number, number>()
    roundedScores.forEach((score) => {
      frequency.set(score, (frequency.get(score) || 0) + 1)
    })
    const modeEntry = Array.from(frequency.entries()).reduce((a, b) => (a[1] > b[1] ? a : b))
    const mode = modeEntry ? modeEntry[0] : 0

    // Range
    const range = Math.max(...scores) - Math.min(...scores)

    // Skewness
    const skewness =
      scores.reduce((sum, score) => sum + Math.pow((score - mean) / standardDeviation, 3), 0) /
      scores.length

    // Kurtosis
    const kurtosis =
      scores.reduce((sum, score) => sum + Math.pow((score - mean) / standardDeviation, 4), 0) /
        scores.length -
      3

    // Reliability (Cronbach's Alpha approximation using KR-20 for dichotomous items)
    const reliability = calculateReliability(data)

    return {
      totalStudents: data.totalStudents,
      totalQuestions: data.totalQuestions,
      meanPercentageScore: mean,
      standardDeviation,
      reliability,
      skewness,
      kurtosis,
      medianScore: median,
      mode,
      range,
    }
  }

  const calculateItemAnalysis = (questionNumber: number, data: ZipGradeCsvData): ItemAnalysis => {
    const responses = data.students
      .map((student) => student.responses.find((r) => r.questionNumber === questionNumber))
      .filter(Boolean) as ItemResponse[]

    if (responses.length === 0) {
      throw new Error(`No responses found for question ${questionNumber}`)
    }

    // Item Difficulty (P-value)
    const correctCount = responses.filter((r) => r.isCorrect).length
    const difficulty = correctCount / responses.length
    const difficultyLevel = getDifficultyLevel(difficulty)

    // Item Discrimination using Upper-Lower 30% method
    const discrimination = calculateDiscrimination(questionNumber, data)
    const discriminationLevel = getDiscriminationLevel(discrimination)

    // Point-biserial correlation
    const pointBiserial = calculatePointBiserial(questionNumber, data)

    // Item statistics
    const scores = responses.map((r) => (r.isCorrect ? 1 : 0))
    const meanScore = scores.reduce((sum: number, score: number) => sum + score, 0) / scores.length
    const variance =
      scores.reduce((sum: number, score: number) => sum + Math.pow(score - meanScore, 2), 0) /
      scores.length
    const standardDeviation = Math.sqrt(variance)

    // Skewness for the item
    const skewness =
      standardDeviation > 0
        ? scores.reduce(
            (sum: number, score: number) =>
              sum + Math.pow((score - meanScore) / standardDeviation, 3),
            0,
          ) / scores.length
        : 0

    // Proposed action based on difficulty and discrimination
    const proposedAction = getProposedAction(difficulty, discrimination)

    // Mastery level (percentage of students achieving 75% or higher on this item)
    const masteryLevel = (correctCount / responses.length) * 100

    return {
      questionNumber,
      difficulty,
      difficultyLevel,
      discrimination,
      discriminationLevel,
      pointBiserial,
      meanScore,
      standardDeviation,
      skewness,
      proposedAction,
      masteryLevel,
    }
  }

  const calculateDiscrimination = (questionNumber: number, data: ZipGradeCsvData): number => {
    // Sort students by total score
    const sortedStudents = [...data.students].sort((a, b) => b.totalScore - a.totalScore)

    // Calculate 30% groups
    const upperCount = Math.floor(sortedStudents.length * 0.3)
    const lowerCount = Math.floor(sortedStudents.length * 0.3)

    const upperGroup = sortedStudents.slice(0, upperCount)
    const lowerGroup = sortedStudents.slice(-lowerCount)

    // Calculate proportion correct in each group
    const upperCorrect = upperGroup.filter((student) => {
      const response = student.responses.find((r) => r.questionNumber === questionNumber)
      return response?.isCorrect || false
    }).length

    const lowerCorrect = lowerGroup.filter((student) => {
      const response = student.responses.find((r) => r.questionNumber === questionNumber)
      return response?.isCorrect || false
    }).length

    const upperProportion = upperCorrect / upperCount
    const lowerProportion = lowerCorrect / lowerCount

    return upperProportion - lowerProportion
  }

  const calculatePointBiserial = (questionNumber: number, data: ZipGradeCsvData): number => {
    const responses = data.students.map((student) => {
      const itemResponse = student.responses.find((r) => r.questionNumber === questionNumber)
      return {
        itemScore: itemResponse?.isCorrect ? 1 : 0,
        totalScore: student.percentage,
      }
    })

    const correctResponses = responses.filter((r) => r.itemScore === 1)
    const incorrectResponses = responses.filter((r) => r.itemScore === 0)

    if (correctResponses.length === 0 || incorrectResponses.length === 0) {
      return 0
    }

    const meanCorrect =
      correctResponses.reduce((sum, r) => sum + r.totalScore, 0) / correctResponses.length
    const meanIncorrect =
      incorrectResponses.reduce((sum, r) => sum + r.totalScore, 0) / incorrectResponses.length

    const allScores = responses.map((r) => r.totalScore)
    const overallMean = allScores.reduce((sum, score) => sum + score, 0) / allScores.length
    const overallStd = Math.sqrt(
      allScores.reduce((sum, score) => sum + Math.pow(score - overallMean, 2), 0) /
        allScores.length,
    )

    const p = correctResponses.length / responses.length
    const q = 1 - p

    if (overallStd === 0) return 0

    return ((meanCorrect - meanIncorrect) / overallStd) * Math.sqrt(p * q)
  }

  const calculateReliability = (data: ZipGradeCsvData): number => {
    // KR-20 formula for dichotomous items
    const k = data.totalQuestions
    const totalVariance = (data.metadata.standardDeviation || 0) ** 2

    let sumPQ = 0
    for (const question of data.questions) {
      const responses = data.students.map((student) =>
        student.responses.find((r) => r.questionNumber === question.questionNumber)?.isCorrect
          ? 1
          : 0,
      )
      const p = responses.reduce((sum: number, score: number) => sum + score, 0) / responses.length
      const q = 1 - p
      sumPQ += p * q
    }

    if (totalVariance === 0) return 0

    return (k / (k - 1)) * (1 - sumPQ / totalVariance)
  }

  const calculateCompetencyAnalysis = (
    data: ZipGradeCsvData,
    competencyMapping: Map<number, string>,
  ): CompetencyAnalysis[] => {
    // Group items by competency
    const competencyGroups = new Map<string, number[]>()

    for (const [questionNumber, competency] of competencyMapping.entries()) {
      if (!competencyGroups.has(competency)) {
        competencyGroups.set(competency, [])
      }
      competencyGroups.get(competency)!.push(questionNumber)
    }

    // Calculate averages for each competency
    const competencyAnalyses: CompetencyAnalysis[] = []

    for (const [competency, items] of competencyGroups.entries()) {
      const itemAnalyses = items.map((questionNumber) =>
        calculateItemAnalysis(questionNumber, data),
      )

      const avgDifficulty =
        itemAnalyses.reduce((sum, analysis) => sum + analysis.difficulty, 0) / itemAnalyses.length
      const avgDiscrimination =
        itemAnalyses.reduce((sum, analysis) => sum + analysis.discrimination, 0) /
        itemAnalyses.length
      const avgMastery =
        itemAnalyses.reduce((sum, analysis) => sum + analysis.masteryLevel, 0) / itemAnalyses.length

      competencyAnalyses.push({
        rank: 0, // Will be set after sorting
        learningCompetency: competency,
        items,
        averageDifficulty: avgDifficulty,
        averageDifficultyLevel: getDifficultyLevel(avgDifficulty),
        averageDiscrimination: avgDiscrimination,
        averageDiscriminationLevel: getDiscriminationLevel(avgDiscrimination),
        averageMasteryLevel: avgMastery,
      })
    }

    // Sort by difficulty (highest to lowest for most learned, lowest to highest for least learned)
    competencyAnalyses.sort((a, b) => b.averageDifficulty - a.averageDifficulty)
    competencyAnalyses.forEach((analysis, index) => {
      analysis.rank = index + 1
    })

    return competencyAnalyses
  }

  const generateSummaryReport = (
    analysis: TestAnalysis,
    competencyMapping?: Map<number, string>,
  ): SummaryReportRow[] => {
    return analysis.itemAnalyses.map((item) => ({
      itemNumber: item.questionNumber,
      learningCompetency: competencyMapping?.get(item.questionNumber) || 'Not Assigned',
      difficulty: Number((item.difficulty * 100).toFixed(2)),
      discriminationIndex: Number(item.discrimination.toFixed(3)),
      proposedAction: item.proposedAction,
      mps: Number(item.meanScore.toFixed(3)),
      standardDeviation: Number(item.standardDeviation.toFixed(3)),
      masteryLevel: Number(item.masteryLevel.toFixed(1)),
    }))
  }

  // Utility functions
  const getDifficultyLevel = (difficulty: number): string => {
    if (difficulty > 0.8) return 'Very Easy'
    if (difficulty >= 0.75) return 'Moderately Easy'
    if (difficulty >= 0.5) return 'Moderate'
    if (difficulty >= 0.25) return 'Moderately Difficult'
    return 'Very Difficult'
  }

  const getDiscriminationLevel = (discrimination: number): string => {
    if (discrimination >= 0.4) return 'Excellent'
    if (discrimination >= 0.3) return 'Good'
    if (discrimination >= 0.2) return 'Fair'
    if (discrimination >= 0.1) return 'Poor'
    return 'Very Poor'
  }

  const getProposedAction = (difficulty: number, discrimination: number): string => {
    if (discrimination < 0) {
      return 'Reject - Negative discrimination'
    }
    if (discrimination < 0.2) {
      if (difficulty > 0.8) {
        return 'Revise - Too easy, poor discrimination'
      }
      if (difficulty < 0.25) {
        return 'Revise - Too difficult, poor discrimination'
      }
      return 'Revise - Poor discrimination'
    }
    if (difficulty > 0.8) {
      return 'Accept but revise - Too easy'
    }
    if (difficulty < 0.25) {
      return 'Accept but revise - Too difficult'
    }
    return 'Accept - Good item'
  }

  const generateRecommendations = (
    testStats: TestStatistics,
    itemAnalyses: ItemAnalysis[],
  ): string[] => {
    const recommendations: string[] = []

    // Test difficulty recommendations
    if (testStats.meanPercentageScore > 80) {
      recommendations.push(
        'Test appears to be too easy. Consider adding more challenging questions.',
      )
    } else if (testStats.meanPercentageScore < 50) {
      recommendations.push(
        'Test appears to be too difficult. Consider revising or removing very difficult items.',
      )
    }

    // Reliability recommendations
    if (testStats.reliability < 0.7) {
      recommendations.push(
        'Test reliability is below acceptable levels. Consider revising poor items or adding more questions.',
      )
    }

    // Item-specific recommendations
    const poorItems = itemAnalyses.filter((item) => item.discrimination < 0.2).length
    if (poorItems > 0) {
      recommendations.push(
        `${poorItems} items have poor discrimination. Review these items for revision or removal.`,
      )
    }

    const negativeItems = itemAnalyses.filter((item) => item.discrimination < 0).length
    if (negativeItems > 0) {
      recommendations.push(
        `${negativeItems} items have negative discrimination. These should be rejected or thoroughly revised.`,
      )
    }

    return recommendations
  }

  return {
    analyzeTest,
    calculateTestStatistics,
    calculateItemAnalysis,
    calculateCompetencyAnalysis,
    generateSummaryReport,
    getDifficultyLevel,
    getDiscriminationLevel,
    getProposedAction,
  }
}
