// Statistical analysis types
export interface ItemAnalysis {
  questionNumber: number
  difficulty: number
  difficultyLevel: string
  discrimination: number
  discriminationLevel: string
  pointBiserial: number
  meanScore: number
  standardDeviation: number
  skewness: number
  proposedAction: string
  masteryLevel: number
}

export interface TestStatistics {
  totalStudents: number
  totalQuestions: number
  meanPercentageScore: number
  standardDeviation: number
  reliability: number
  skewness: number
  kurtosis: number
  medianScore: number
  mode: number
  range: number
}

export interface CompetencyAnalysis {
  rank: number
  learningCompetency: string
  items: number[]
  averageDifficulty: number
  averageDifficultyLevel: string
  averageDiscrimination: number
  averageDiscriminationLevel: string
  averageMasteryLevel: number
}

export interface TestAnalysis {
  testStatistics: TestStatistics
  itemAnalyses: ItemAnalysis[]
  competencyAnalysis?: CompetencyAnalysis[]
  recommendations: string[]
}

export interface SummaryReportRow {
  itemNumber: number
  learningCompetency: string
  difficulty: number
  discriminationIndex: number
  proposedAction: string
  mps: number
  standardDeviation: number
  masteryLevel: number
}
