<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h2 class="text-2xl font-bold text-card-foreground">Item Analysis Summary</h2>
        <p class="text-muted-foreground mt-1">
          Comprehensive statistical analysis of test items
        </p>
      </div>
      <ItemAnalysisExportButton
        :data="summaryData"
        :analysis="analysis"
        :competency-mapping="competencyMapping"
        @export="handleExport"
      />
    </div>

    <!-- Test Overview -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
      <UCard>
        <template #header>
          <div class="flex items-center gap-2">
            <UIcon name="i-heroicons-users" class="w-5 h-5 text-primary" />
            <span class="font-medium">Students</span>
          </div>
        </template>
        <div class="text-2xl font-bold text-primary">
          {{ analysis.testStatistics.totalStudents }}
        </div>
      </UCard>

      <UCard>
        <template #header>
          <div class="flex items-center gap-2">
            <UIcon name="i-heroicons-list-bullet" class="w-5 h-5 text-primary" />
            <span class="font-medium">Questions</span>
          </div>
        </template>
        <div class="text-2xl font-bold text-primary">
          {{ analysis.testStatistics.totalQuestions }}
        </div>
      </UCard>

      <UCard>
        <template #header>
          <div class="flex items-center gap-2">
            <UIcon name="i-heroicons-chart-bar" class="w-5 h-5 text-primary" />
            <span class="font-medium">Mean Score</span>
          </div>
        </template>
        <div class="text-2xl font-bold text-primary">
          {{ analysis.testStatistics.meanPercentageScore.toFixed(1) }}%
        </div>
      </UCard>

      <UCard>
        <template #header>
          <div class="flex items-center gap-2">
            <UIcon name="i-heroicons-check-circle" class="w-5 h-5 text-primary" />
            <span class="font-medium">Reliability</span>
          </div>
        </template>
        <div class="text-2xl font-bold text-primary">
          {{ analysis.testStatistics.reliability.toFixed(3) }}
        </div>
      </UCard>
    </div>



    <!-- Summary Table -->
    <UCard>
      <template #header>
        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-lg font-semibold">Item Analysis Results</h3>
            <p class="text-sm text-muted-foreground mt-1">
              Click on Learning Competency cells to edit them directly
            </p>
          </div>
          <div class="text-sm text-muted-foreground">
            {{ summaryData.length }} items
          </div>
        </div>
      </template>

      <UTable
        :data="tableData"
        :columns="columns"
        :loading="false"
        class="w-full"
      >
        <template #learningCompetency="{ row }">
          <div class="flex items-center">
            <UInput
              :model-value="getCompetencyValue((row as any).itemNumber)"
              @update:model-value="(value: string) => updateCompetency((row as any).itemNumber, value)"
              placeholder="Enter learning competency..."
              size="sm"
              class="min-w-64"
              variant="outline"
            />
          </div>
        </template>
        
        <template #empty-state>
          <div class="flex flex-col items-center justify-center py-6 gap-3">
            <UIcon name="i-heroicons-circle-stack-20-solid" class="w-8 h-8 text-gray-400" />
            <span class="italic text-sm">No analysis data available.</span>
          </div>
        </template>
      </UTable>
    </UCard>

    <!-- Recommendations -->
    <UAlert
      v-if="analysis.recommendations.length > 0"
      color="info"
      variant="soft"
      title="Test Analysis Recommendations"
    >
      <template #description>
        <ul class="list-disc list-inside space-y-1">
          <li v-for="recommendation in analysis.recommendations" :key="recommendation">
            {{ recommendation }}
          </li>
        </ul>
      </template>
    </UAlert>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import type { TestAnalysis, SummaryReportRow } from '~/types/statistics'
import type { TableColumn } from '@nuxt/ui'

// Component props
interface Props {
  analysis: TestAnalysis
  competencyMapping?: Map<number, string>
}

const props = defineProps<Props>()

// Component emits
interface Emits {
  (e: 'export', format: 'excel' | 'csv'): void
  (e: 'update:competency-mapping', mapping: Map<number, string>): void
}

const emit = defineEmits<Emits>()

// Composables
const { generateSummaryReport, getDifficultyLevel, getDiscriminationLevel } = useStatistics()

// Local competency mapping state for editing
const localCompetencyMapping = ref<Map<number, string>>(new Map())

// Initialize local mapping from props
watch(() => props.competencyMapping, (newMapping) => {
  if (newMapping) {
    localCompetencyMapping.value = new Map(newMapping)
  }
}, { immediate: true })

// Update competency for a specific item
const updateCompetency = (itemNumber: number, competency: string) => {
  localCompetencyMapping.value.set(itemNumber, competency)
  emit('update:competency-mapping', new Map(localCompetencyMapping.value))
}

// Get competency value for display
const getCompetencyValue = (itemNumber: number): string => {
  return localCompetencyMapping.value.get(itemNumber) || 'Not Assigned'
}

// Computed properties
const summaryData = computed(() => 
  generateSummaryReport(props.analysis, localCompetencyMapping.value)
)

// Function to get mastery level description based on percentage
const getMasteryLevelDescription = (percentage: number): string => {
  if (percentage >= 96) return 'Mastered'
  if (percentage >= 86) return 'Closely Approximating Mastery'
  if (percentage >= 66) return 'Moving Towards Mastery'
  if (percentage >= 35) return 'Average Near Mastery'
  if (percentage >= 15) return 'Low Mastery'
  if (percentage >= 5) return 'Very Low Mastery'
  return 'Absolutely No Mastery'
}

// Columns definition for UTable
const columns: TableColumn<any>[] = [
  {
    accessorKey: 'itemNumber',
    header: 'Item #'
  },
  {
    accessorKey: 'learningCompetency',
    header: 'Learning Competency'
  },
  {
    accessorKey: 'difficulty',
    header: 'Difficulty (%)'
  },
  {
    accessorKey: 'discriminationIndex',
    header: 'Discrimination'
  },
  {
    accessorKey: 'proposedAction',
    header: 'Proposed Action'
  },
  {
    accessorKey: 'mps',
    header: 'MPS'
  },
  {
    accessorKey: 'standardDeviation',
    header: 'SD'
  },
  {
    accessorKey: 'masteryLevel',
    header: 'Mastery Level'
  }
]

// Table data with transformed mastery level
const tableData = computed(() => {
  const data = summaryData.value.map(item => ({
    ...item,
    difficulty: `${item.difficulty}%`,
    discriminationIndex: item.discriminationIndex.toFixed(2),
    mps: item.mps.toFixed(1),
    standardDeviation: item.standardDeviation.toFixed(2),
    masteryLevel: getMasteryLevelDescription(item.masteryLevel)
  }))  
  return data
})

// Methods
const handleExport = (format: 'excel' | 'csv') => {
  emit('export', format)
}
</script>
