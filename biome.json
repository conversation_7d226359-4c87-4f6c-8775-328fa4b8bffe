{"$schema": "https://biomejs.dev/schemas/2.2.0/schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "files": {"ignoreUnknown": false, "include": ["**/*.js", "**/*.ts", "**/*.jsx", "**/*.tsx", "**/*.json", "**/*.jsonc"]}, "formatter": {"enabled": true, "indentStyle": "space", "lineWidth": 100}, "linter": {"enabled": true, "rules": {"recommended": true}}, "overrides": [{"includes": ["**/*.vue"], "linter": {"rules": {"correctness": {"noUnusedVariables": "off"}, "style": {"useImportType": "off"}}}}, {"includes": ["**/*.ts", "**/*.tsx"], "linter": {"rules": {"style": {"useImportType": "warn"}}}}, {"includes": ["**/*.css"], "linter": {"rules": {"suspicious": {"noUnknownAtRules": "off"}}}}], "javascript": {"formatter": {"quoteStyle": "single", "semicolons": "asNeeded"}}, "assist": {"enabled": true, "actions": {"source": {"organizeImports": "on"}}}}